#pragma once

#include <cmath>
#include <optional>
#include <string>

#include <proj_utils/cpp/geod.hpp>
#include <proj_utils/cpp/point.hpp>
namespace carbon::proj {
class RelativePosCoord {
  static constexpr double RIGHT_ANGLE_RAD = 90.0 * M_PI / 180.0;

public:
  static inline double azimuth_to_xy_theta(double azimuth_rad) { return RIGHT_ANGLE_RAD - azimuth_rad; }
  RelativePosCoord(std::optional<Point> start, std::optional<std::string> ellipse);
  inline void set_start(const Point &start) { start_ = start; }
  bool start_is_valid() const { return start_.has_value(); }
  Point geo_to_rel(const Point &geo_pos, const double &azm_offset_deg = 0.0);
  inline const Geod &geod() const { return geod_; }

private:
  std::optional<Point> start_;
  Geod geod_;
};
} // namespace carbon::proj
