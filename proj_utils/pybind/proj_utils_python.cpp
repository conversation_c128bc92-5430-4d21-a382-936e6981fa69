#include <pybind11/functional.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include "proj_utils/cpp/ellipse.hpp"
#include "proj_utils/cpp/geod.hpp"
#include "proj_utils/cpp/point.hpp"
#include "proj_utils/cpp/relative_pos_coord.hpp"

#include <geodesic.h>

namespace py = pybind11;

namespace carbon::proj {

std::vector<std::string> available_ellipses() {
  std::vector<std::string> names;
  for (const auto &kv : list_ellipse()) {
    names.emplace_back(kv.first);
  }
  return names;
}
PYBIND11_MODULE(proj_utils_python, m) {
  m.def("available_ellipses", &available_ellipses, py::call_guard<py::gil_scoped_release>());
  py::class_<Geod, std::shared_ptr<Geod>>(m, "Geod")
      .def(py::init<const std::string &>(), py::arg("ellipse"), py::call_guard<py::gil_scoped_release>())
      .def("inv",
           py::overload_cast<const double &, const double &, const double &, const double &>(&Geod::inv, py::const_),
           py::arg("lon_start"), py::arg("lat_start"), py::arg("lon_end"), py::arg("lat_end"),
           py::call_guard<py::gil_scoped_release>())
      .def("direct",
           py::overload_cast<const double &, const double &, const double &, const double &>(&Geod::direct, py::const_),
           py::arg("lon_start"), py::arg("lat_start"), py::arg("azimuth_deg"), py::arg("dist_meters"),
           py::call_guard<py::gil_scoped_release>());

  py::class_<RelativePosCoord, std::shared_ptr<RelativePosCoord>>(m, "RelativePosCoord")
      .def_static("azimuth_to_xy_theta", RelativePosCoord::azimuth_to_xy_theta, py::arg("azimuth_rad"),
                  py::call_guard<py::gil_scoped_release>())
      .def(py::init<std::optional<Point>, std::optional<std::string>>(), py::arg("start"), py::arg("ellipse"),
           py::call_guard<py::gil_scoped_release>())
      .def("set_start", &RelativePosCoord::set_start, py::arg("start"), py::call_guard<py::gil_scoped_release>())
      .def("start_is_valid", &RelativePosCoord::start_is_valid, py::call_guard<py::gil_scoped_release>())
      .def("geo_to_rel", &RelativePosCoord::geo_to_rel, py::arg("geo_pos"), py::arg("azm_offset_deg") = 0.0,
           py::call_guard<py::gil_scoped_release>())
      .def_property_readonly("geod", &RelativePosCoord::geod, py::return_value_policy::reference_internal);

  py::class_<Point, std::shared_ptr<Point>>(m, "Point")
      .def(py::init<const double &, const double &>(), py::arg("_x"), py::arg("_y"),
           py::call_guard<py::gil_scoped_release>())
      .def_property("lon", &Point::lon, &Point::set_lon, py::call_guard<py::gil_scoped_release>())
      .def_property("lat", &Point::lat, &Point::set_lat, py::call_guard<py::gil_scoped_release>())
      .def_property("x", &Point::x, &Point::set_x, py::call_guard<py::gil_scoped_release>())
      .def_property("y", &Point::y, &Point::set_y, py::call_guard<py::gil_scoped_release>());
}

} // namespace carbon::proj