#pragma once

#include <memory>
#include <string>
#include <tuple>

#include "proj_utils/cpp/point.hpp"

struct geod_geodesic;

namespace carbon::proj {
class Geod {
public:
  Geod(const std::string &ellipse);
  std::tuple<double, double> inv(const double &lon_start, const double &lat_start, const double &lon_end,
                                 const double &lat_end) const; // returns azimuth_deg dist_meters

  inline std::tuple<double, double> inv(const Point &start, const Point &end) const {
    return inv(start.lon(), start.lat(), end.lon(), end.lat());
  }
  Point direct(const double &lon_start, const double &lat_start, const double &azimuth_deg,
               const double &dist_meters) const;
  inline Point direct(const Point &start, const double &azimuth_deg, const double &dist_meters) const {
    return direct(start.lon(), start.lat(), azimuth_deg, dist_meters);
  }

private:
  std::unique_ptr<geod_geodesic> geod_;
};

} // namespace carbon::proj
