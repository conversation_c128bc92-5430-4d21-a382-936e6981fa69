import logging
import os
import traceback
from functools import wraps
from typing import Any, Callable

import torch
from torch.distributed.elastic.multiprocessing import SignalEx<PERSON>, Std
from torch.distributed.elastic.multiprocessing.errors import ChildFailedError
from torch.distributed.launcher.api import LaunchConfig, elastic_launch

LOG = logging.getLogger(__name__)


def launcher_error_handler(func: Callable[..., Any]) -> Callable[..., Any]:
    @wraps(func)
    def error_handler(*args: Any, **kwargs: Any) -> None:
        try:
            func(*args, **kwargs)
            torch.distributed.barrier()
        except ChildFailedError as e:
            LOG.error(f"Child failed error on rank {os.getenv('RANK', '?')}: {e}\n{traceback.format_exc()}")
            raise e
        except SignalException as e:
            LOG.error(f"Signal Exception on rank {os.getenv('RANK', '?')}: {e}\n{traceback.format_exc()}")
            raise e
        except Exception as e:
            LOG.info(f"Exception running job on rank {os.getenv('RANK', '?')}: {e}\n{traceback.format_exc()}")
            raise e
        finally:
            # Cleanup the process group
            torch.distributed.barrier()
            torch.distributed.destroy_process_group()

    return error_handler


def default(*args: Any) -> None:
    LOG.info(f"Running default: {args}")


def distributed_elastic_launcher(
    rdzv_endpoint: str = "127.0.0.1:29500",
    nodes: int = 1,
    nproc_per_node: int = 1,
    fn: Callable[..., Any] = default,
    failure_fn: Callable[[], Any] = default,
    *args: Any,
) -> None:
    LOG.info(f"==== STARTING {fn.__name__}() ====")
    try:
        os.environ["OMP_NUM_THREADS"] = str(1)
        launch_config = LaunchConfig(
            min_nodes=nodes,
            max_nodes=nodes,
            nproc_per_node=nproc_per_node,
            run_id="none",
            role="default",
            rdzv_endpoint=rdzv_endpoint,
            rdzv_backend="c10d",
            rdzv_configs={"rank": 0, "timeout": 900},
            rdzv_timeout=-1,
            max_restarts=0,
            monitor_interval=5,
            start_method="spawn",
            metrics_cfg={},
            local_addr=None,
            log_dir=None,
            redirects=Std.from_str("0"),
            tee=Std.from_str("0"),
        )
        elastic_launch(config=launch_config, entrypoint=fn)(*args)
    except Exception as e:
        failure_fn()
        raise e
    finally:
        LOG.info(f"==== COMPLETED {fn.__name__}() ====")


def elastic_launcher(
    nproc_per_node: int = 1, fn: Callable[..., Any] = default, failure_fn: Callable[[], Any] = default, *args: Any
) -> None:
    distributed_elastic_launcher("127.0.0.1:29500", 1, nproc_per_node, fn, failure_fn, *args)
