import json
import logging
import os
import random
import uuid
from collections import defaultdict
from operator import or_
from typing import Any, Dict, List, Optional, Set, Tuple, cast

import boto3
import numpy as np
import pandas as pd
import torch
from sklearn.decomposition import PCA
from sklearn.preprocessing import normalize
from sqlalchemy import String, and_
from sqlalchemy import cast as sql_cast
from sqlalchemy import create_engine, event, select
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session

from deeplearning.comparison.config import ComparisonConfig
from deeplearning.comparison.constants import (
    DATASOURCE_MANUAL,
    DATASOURCE_SIZE_DIFFERENTIATED_GENERATED,
    SIZE_BUCKETS,
    WEED_CATEGORIES,
)
from deeplearning.comparison.tables import (
    Base,
    ComparisonEmbeddingObject,
    ComparisonEmbeddings,
    ComparisonLabels,
    ComparisonPredictionAndLabel,
    ComparisonReviews,
)
from deeplearning.constants import CARBON_DATA_DIR, CROP_ID_TO_LABEL, Environment
from deeplearning.deepweed.dataset_utils import extract_image_ids_from_dataset
from deeplearning.embeddings.io import (
    EmbeddingDatapoint,
    EmbeddingDatapointMetadata,
    EmbeddingDataset,
    EmbeddingDatasetMetadata,
)
from deeplearning.scripts.datasets.get_uuids import does_square_a_overlap_center_b, does_square_a_overlap_circle_b
from deeplearning.utils.download_utils import download_records
from deeplearning.utils.uuid import get_globally_unique_id
from lib.common.time.sleep import sleep_ms
from lib.common.time.time import maka_control_timestamp_ms
from lib.common.veselka.client import VeselkaClient

LOG = logging.getLogger(__name__)
SAVED_H5_FILENAME = "test-embeddings.hdf5"
SAVED_CSV_FILENAME = "point_uuids.csv"


def get_comparison_evaluation_bucket() -> str:
    return "carbon-comparison-evaluations"


def get_comparison_evaluation_prefix(model_id: str, environment: Environment) -> str:
    env = "production"
    if environment != Environment.PRODUCTION:
        env = "staging"
    return f"{env}/comparison_evaluations/{model_id}"


def get_comparison_db_prefix(model_id: str, dataset_id: Optional[str] = None) -> str:
    if dataset_id is not None:
        return f"comparison_db/{model_id}/{dataset_id}"
    else:
        return f"comparison_db/{model_id}"


def get_images_with_comparison_embeddings(model_id: str) -> Tuple[Set[str], str]:
    s3 = boto3.client("s3")
    paginator = s3.get_paginator("list_objects_v2")
    pages = paginator.paginate(
        Bucket=get_comparison_evaluation_bucket(),
        Prefix=get_comparison_evaluation_prefix(model_id, Environment.PRODUCTION),
    )

    image_ids: Set[str] = set()
    for page in pages:
        if "Contents" in page:
            for obj in page["Contents"]:
                image_ids.add(os.path.splitext(os.path.basename(obj["Key"]))[0])

    local_dir = os.path.join(
        get_comparison_evaluation_dir(), get_comparison_evaluation_prefix(model_id, Environment.PRODUCTION)
    )

    if os.path.exists(local_dir):
        for file in os.listdir(local_dir):
            if os.path.isfile(os.path.join(local_dir, file)):
                image_ids.add(os.path.splitext(file)[0])

    return image_ids, local_dir


def px2mm(size_px: float) -> float:
    return size_px * 25.4 / 200


def get_size_bucket(size: float) -> int:
    bucket = 0
    for min in SIZE_BUCKETS:
        if min > size:
            break
        bucket = min

    return bucket


def get_category_key(classes: Tuple[str, str]) -> Tuple[str, str]:
    clsses = sorted(list(classes))
    class_a = clsses[0]
    class_b = clsses[1]
    return (class_a, class_b)


def get_size_key(sizes: Tuple[Any, Any]) -> Tuple[str, str]:
    size_min = get_size_bucket(min(sizes))
    size_max = get_size_bucket(max(sizes))
    return (str(size_min), str(size_max))


def get_engine(db_path: str) -> Engine:
    engine = create_engine(f"sqlite:///{db_path}?_pragma=busy_timeout(5000)")

    def _fk_pragma_on_connect(dbapi_connection: Engine, connection_record: Any) -> None:
        dbapi_connection.execute("pragma foreign_keys=ON")

    event.listen(engine, "connect", _fk_pragma_on_connect)
    return engine


def get_embeddings_engine(db_path: str, generate_table: bool = True) -> Engine:
    engine = get_engine(db_path)
    if generate_table:
        Base.metadata.create_all(engine, tables=[ComparisonEmbeddings.__table__])
    return engine


def get_predictions_and_labels_engine(db_path: str, generate_table: bool = True) -> Engine:
    engine = get_engine(db_path)
    try:
        if generate_table:
            Base.metadata.create_all(engine, tables=[ComparisonPredictionAndLabel.__table__])
    except Exception as e:
        LOG.warning(f"Could not generate table. Does it already exist? {e}")
    return engine


def get_labels_engine(db_path: str) -> Engine:
    engine = get_engine(db_path)
    Base.metadata.create_all(engine, tables=[ComparisonLabels.__table__])
    return engine


def create_hash_key_for_comparison_label(
    label: ComparisonLabels, round_digits: Optional[int] = 3
) -> Tuple[Tuple[str, float, float], ...]:
    if round_digits is not None:
        hash_key = tuple(
            sorted(
                [
                    (
                        str(label.image_one),
                        round(float(label.label_one_x), round_digits),
                        round(float(label.label_one_y), round_digits),
                    ),
                    (
                        str(label.image_two),
                        round(float(label.label_two_x), round_digits),
                        round(float(label.label_two_y), round_digits),
                    ),
                ]
            )
        )
    else:
        hash_key = tuple(
            sorted(
                [
                    (str(label.image_one), float(label.label_one_x), float(label.label_one_y)),
                    (str(label.image_two), float(label.label_two_x), float(label.label_two_y)),
                ]
            )
        )

    return hash_key


def create_hash_key_for_overlapping_point(
    x: float, y: float, category: str, round_digits: Optional[int] = 3
) -> Tuple[float, float, str]:
    # The hash_key is used for efficiently retrieving specific points and accessing their overlapping information when needed.
    if round_digits is not None:
        hash_key = (
            round(x, round_digits),
            round(y, round_digits),
            category,
        )
    else:
        hash_key = (x, y, category)

    return hash_key


def download_comparison_data(
    s3_key_prefix: str,
    data_dir: str,
    comparison_config: ComparisonConfig,
    only_verified_data: bool = False,
    fast_run: bool = False,
) -> Tuple[Dict[str, str], List[Dict[str, Any]], str, Dict[str, Optional[str]], Dict[str, Tuple[int, int]]]:
    db_path = os.path.join(data_dir, "comparison_labels.db")

    if not os.path.exists(db_path):
        os.makedirs(data_dir, exist_ok=True)
        bucket = boto3.resource("s3").Bucket("carbon-cloud-app")
        bucket.download_file(f"{s3_key_prefix}/comparison_labels.db", db_path)

    images_and_labels_path = os.path.join(data_dir, "images_and_labels.json")
    if not os.path.exists(images_and_labels_path):
        engine = get_engine(db_path)
        with Session(engine) as session:
            comparison_labels = session.query(ComparisonLabels).filter(
                and_(ComparisonLabels.valid.is_(True), ComparisonLabels.match.is_not(None))
            )

            if only_verified_data:
                # [RAVEN] casting to String seems to be necessary, it's unclear why exactly. Otherwise the join fails silently sometimes. Likely type affinity related https://www.sqlite.org/datatype3.html
                review_subquery = select([sql_cast(ComparisonReviews.label_id, String)]).where(
                    ComparisonReviews.valid.is_(True)
                )
                comparison_labels = comparison_labels.filter(sql_cast(ComparisonLabels.id, String).in_(review_subquery))

            if comparison_config.db_filter_start_time_ms is not None:
                comparison_labels = comparison_labels.filter(
                    ComparisonLabels.created > comparison_config.db_filter_start_time_ms
                )
            if comparison_config.db_filter_end_time_ms is not None:
                comparison_labels = comparison_labels.filter(
                    ComparisonLabels.created < comparison_config.db_filter_end_time_ms
                )

            if comparison_config.db_filter_max_size_multiplier is not None:
                comparison_labels = comparison_labels.filter(
                    or_(
                        ComparisonLabels.label_two_radius.between(
                            ComparisonLabels.label_one_radius / comparison_config.db_filter_max_size_multiplier,
                            ComparisonLabels.label_one_radius * comparison_config.db_filter_max_size_multiplier,
                        ),
                        ComparisonLabels.source == DATASOURCE_MANUAL,
                    )
                )

            if fast_run:
                comparison_labels = comparison_labels.limit(500)

            comparison_labels = comparison_labels.all()

        if comparison_config.label_generation_size_based_enabled:
            new_comparison_labels = generate_samples_outside_of_size_limit(
                n=comparison_config.label_generation_size_based_n,
                size_multiple=comparison_config.label_generation_size_based_multiplier,
                comparison_labels=comparison_labels,
            )

            comparison_labels += new_comparison_labels

            with Session(engine) as session:
                session.bulk_save_objects(new_comparison_labels)
                session.commit()

            LOG.info(f"Generated {len(new_comparison_labels)} items of different sizes")

        image_ids: Set[str] = set()
        labels: List[Dict[str, Any]] = []
        unique_comparison_set = set()
        count_same_pair, count_redundant_pair = 0, 0

        for label in comparison_labels:
            # Remove pairs where point_a == point_b.
            if (
                label.image_one == label.image_two
                and label.label_one_x == label.label_two_x
                and label.label_one_y == label.label_two_y
            ):
                count_same_pair += 1
                continue

            # Remove redundant pairs (only add if not seen before).
            hash_key = create_hash_key_for_comparison_label(label=label)

            if hash_key in unique_comparison_set:
                count_redundant_pair += 1
                continue

            unique_comparison_set.add(hash_key)
            image_ids.add(label.image_one)
            image_ids.add(label.image_two)
            label_item = {
                "id": label.id,
                "match": label.match,
                "size": get_size_key((px2mm(label.label_one_radius), px2mm(label.label_two_radius))),
                "category": get_category_key((label.label_one_category, label.label_two_category)),
            }
            labels.append(label_item)

        LOG.info(f"Skip same pairs: {count_same_pair}, redundant pairs: {count_redundant_pair}")
        image_id_list: List[str] = list(image_ids)
        client = VeselkaClient()
        image_ids_to_urls = {}
        image_ids_to_geo = {}
        image_ids_to_height_width = {}
        increment = 100000
        for i in range(0, len(image_id_list), increment):
            urls, geo, height_width = client.get_image_ids_to_meta(image_id_list[i : i + increment])
            image_ids_to_urls.update(urls)
            image_ids_to_geo.update(geo)
            image_ids_to_height_width.update(height_width)

        with open(images_and_labels_path, "w") as f:
            json.dump(
                {
                    "image_ids_to_urls": image_ids_to_urls,
                    "labels": labels,
                    "image_ids_to_geo": image_ids_to_geo,
                    "image_ids_to_height_width": image_ids_to_height_width,
                },
                f,
            )
    else:
        with open(images_and_labels_path) as f:
            images_and_labels = json.load(f)

        image_ids_to_urls = images_and_labels["image_ids_to_urls"]
        labels = images_and_labels["labels"]
        image_ids_to_geo = images_and_labels["image_ids_to_geo"]
        image_ids_to_height_width = images_and_labels["image_ids_to_height_width"]

    return image_ids_to_urls, labels, db_path, image_ids_to_geo, image_ids_to_height_width


def split_label_ids(
    label_ids: List[str], train_split_percentage: float = 0.7, validation_split_percentage: float = 0.1
) -> Dict[str, List[str]]:
    random.seed(1)
    random.shuffle(label_ids)

    train_upper = int(len(label_ids) * train_split_percentage)
    validation_upper = int(train_upper + len(label_ids) * validation_split_percentage)

    return {
        "train": label_ids[:train_upper],
        "validation": label_ids[train_upper:validation_upper],
        "test": label_ids[validation_upper:],
    }


def split_labels(
    labels: List[Dict[str, Any]], train_split_percentage: float = 0.7, validation_split_percentage: float = 0.1
) -> Dict[str, List[str]]:
    random.seed(1)
    label_buckets = defaultdict(list)
    label_set = set()

    for label in labels:
        key = (
            label["match"],
            tuple(label["size"]),
            tuple(label["category"]),
        )  # Use tuple() to convert a list into a tuple to ensure it is hashable.
        if label["id"] not in label_set:
            label_buckets[key].append(label["id"])
            label_set.add(label["id"])

    train: List[str] = []
    validation: List[str] = []
    test: List[str] = []

    labels_to_split = []
    for key, label_ids in label_buckets.items():
        labels_to_split.extend(label_ids)

        if len(labels_to_split) < 20:
            continue
        random.shuffle(labels_to_split)

        train_upper = int(len(labels_to_split) * train_split_percentage)
        validation_upper = int(train_upper + len(labels_to_split) * validation_split_percentage)

        train.extend(labels_to_split[:train_upper])
        validation.extend(labels_to_split[train_upper:validation_upper])
        test.extend(labels_to_split[validation_upper:])
        labels_to_split = []

    # If we finish with some small buckets, we can split them now. No sense in throwing them away
    if len(labels_to_split) > 0:
        random.shuffle(labels_to_split)

        train_upper = int(len(labels_to_split) * train_split_percentage)
        validation_upper = int(train_upper + len(labels_to_split) * validation_split_percentage)

        train.extend(labels_to_split[:train_upper])
        validation.extend(labels_to_split[train_upper:validation_upper])
        test.extend(labels_to_split[validation_upper:])

    return {
        "train": train,
        "validation": validation,
        "test": test,
    }


def send_embeddings_to_db(
    session: Session, embeddings: List[ComparisonEmbeddings], embeddings_set: Set[Tuple[str, ...]]
) -> Set[Tuple[str, ...]]:
    previous_info = embeddings_set
    embeddings_to_save = []
    for embedding in embeddings:
        embedding_key: Tuple[str, ...] = (
            embedding.image_id,
            embedding.x,
            embedding.y,
            embedding.radius,
            embedding.category,
        )
        if embedding_key in previous_info:
            continue

        embeddings_to_save.append(embedding)
        previous_info.add(embedding_key)

    for _ in range(3):
        try:
            session.bulk_save_objects(embeddings_to_save)
            session.commit()
            break
        except Exception as e:
            LOG.warning(f"Error commiting to db {e}")
            session.rollback()
            sleep_ms(250)

    return previous_info


def send_predictions_to_db(
    session: Session, label_ids: List[str], predictions: torch.Tensor, targets: torch.Tensor
) -> None:
    comparison_predictions = []
    for i, label_id in enumerate(label_ids):
        prediction = predictions[i].item()
        target = targets[i].item()
        comparison_predictions.append(ComparisonPredictionAndLabel(id=label_id, prediction=prediction, target=target))

    for _ in range(3):
        try:
            session.bulk_save_objects(comparison_predictions)
            session.commit()
            break
        except Exception as e:
            LOG.warning(f"Error commiting predictions to db {e}")
            session.rollback()
            sleep_ms(100)


def convert_categories(categories: List[str]) -> List[str]:
    # Loading JSON File for category conversion
    cropids = CROP_ID_TO_LABEL

    converted_categories = []
    for category in categories:
        converted_categories.append(cropids.get(category, category).lower())
    return converted_categories


def sample_embeddings(categories: List[str], num_samples: int = 1000) -> List[int]:
    classes = list(set(categories))
    indices = {clz: [x for x in range(len(categories)) if categories[x] == clz] for clz in classes}
    sampled_indices = []
    for x in range(min(len(categories), num_samples)):
        clz = random.choice(classes)

        idx = random.choice(indices[clz])

        sampled_indices.append(idx)
        indices[clz].remove(idx)

        if len(indices[clz]) == 0:
            classes.remove(clz)

    return sampled_indices


def embeddings_to_dataframes(
    embeddings: List[ComparisonEmbeddingObject], num_samples: int = 1000, pca_components: Optional[int] = 50
) -> Tuple[pd.DataFrame, pd.DataFrame, List[int], int]:
    embeddings_list = []
    embeddings_metadata_list = []
    for dictionary in [embedding.to_dict() for embedding in embeddings]:
        embedding = np.array(dictionary["embedding"]).squeeze()
        dictionary.pop("embedding")

        # Converting to Pandas Series
        embedding = pd.Series(embedding)
        embedding_metadata = pd.Series(dictionary)
        # Appending to List
        embeddings_list.append(embedding)
        embeddings_metadata_list.append(embedding_metadata)

    # Converting to Pandas Dataframe
    embeddings_array = np.array(embeddings_list)
    embeddings_df = pd.DataFrame(embeddings_array)
    embeddings_metadata = pd.DataFrame(embeddings_metadata_list)

    number_components = 2
    if pca_components is not None:
        number_components = (
            pca_components if embeddings_array.shape[0] > pca_components else embeddings_array.shape[0] - 1
        )
        pca = PCA(n_components=number_components)

        embeddings_array = normalize(embeddings_array)
        embeddings_df = pd.DataFrame(pca.fit_transform(embeddings_array))

    # Converting Categories into Useful Labels
    embeddings_df[number_components] = convert_categories(embeddings_metadata["category"].tolist())

    # Getting 1000 Random Embeddings to Visualize
    random_indices = sample_embeddings(embeddings_df[number_components].tolist(), num_samples=num_samples)

    embeddings_df = embeddings_df.iloc[random_indices]
    embeddings_metadata = embeddings_metadata.iloc[random_indices].reset_index()

    return embeddings_df, embeddings_metadata, random_indices, number_components


def image_within_border(image_data: Dict[str, Any], x: int, y: int, border: int = 100) -> bool:
    xmin = border
    xmax = image_data["width"] - border
    ymin = border
    ymax = image_data["height"] - border

    return cast(bool, (xmin <= x <= xmax) and (ymin <= y <= ymax))


def default_list_dict() -> Dict[str, List[Dict[str, Any]]]:
    return defaultdict(list)


def default_bool_dict() -> Dict[Tuple[float, float, str], bool]:
    return defaultdict(bool)


def get_all_image_annotation_information_from_file(
    path: str,
    ignored_image_ids: Optional[Set[str]] = None,
    dataset_version: int = 1,
    weed_categories: str = WEED_CATEGORIES,
) -> Tuple[
    List[Tuple[str, str]],
    Dict[str, Dict[Tuple[float, float, str], bool]],
    Dict[str, Dict[Tuple[float, float, str], bool]],
]:
    ignored_image_ids = ignored_image_ids if ignored_image_ids is not None else set()
    weed_categories_set = set(weed_categories.split(","))
    all_image_annotation_ids: Set[Tuple[str, str]] = set()
    points_info_dict: Dict[str, Dict[str, List[Dict[str, Any]]]] = defaultdict(default_list_dict)

    if dataset_version == 1:
        with open(path) as f:
            file = json.load(f)

        for ann in file["annotations"]:
            if "id" in ann and ann["image_id"] not in ignored_image_ids:
                if ann["annotation_type"] == "point" and ann.get("confidence", 0) == 2:
                    category = "weed" if ann["label"] in weed_categories_set else "crop"
                    point_info = {"x": ann["x"], "y": ann["y"], "radius": ann["radius"], "label": ann["label"]}
                    points_info_dict[ann["image_id"]][category].append(point_info)

                all_image_annotation_ids.add((ann["image_id"], ann["image_id"] + "::" + str(ann["id"])))
    elif dataset_version == 2:
        with open(path) as f:
            file = [json.loads(line) for line in f]

        point_category_data = VeselkaClient().get_point_categories()
        weed_categories_set = set([cat["id"] for cat in point_category_data if cat["name"] in weed_categories_set])

        for img in file:
            if img["image_id"] not in ignored_image_ids:
                for point in img["points"]:
                    if "id" in point:
                        if point.get("confidence", 0) == 2:
                            category = "weed" if point["point_category_id"] in weed_categories_set else "crop"
                            point_info = {
                                "x": point["x"],
                                "y": point["y"],
                                "radius": point["radius"],
                                "label": point["point_category_id"],
                            }
                            points_info_dict[img["image_id"]][category].append(point_info)

                        all_image_annotation_ids.add((img["image_id"], img["image_id"] + "::" + str(point["id"])))

    all_image_annotation_ids_list = sorted(list(all_image_annotation_ids), key=lambda x: x[0],)
    overlapped_points_by_circle_dict, overlapped_points_by_center_dict = create_images_overlapped_points_dict(
        points_info_dict=points_info_dict
    )

    return all_image_annotation_ids_list, overlapped_points_by_circle_dict, overlapped_points_by_center_dict


def create_image_overlapped_points_dict(
    point_a_list: List[Dict[str, Any]], point_b_list: List[Dict[str, Any]]
) -> Tuple[Dict[Tuple[float, float, str], bool], Dict[Tuple[float, float, str], bool]]:
    overlapped_point_by_circle_dict: Dict[Tuple[float, float, str], bool] = {}
    overlapped_point_by_center_dict: Dict[Tuple[float, float, str], bool] = {}

    for point_a in point_a_list:
        hash_key = create_hash_key_for_overlapping_point(x=point_a["x"], y=point_a["y"], category=point_a["label"])
        overlapped_point_by_circle_dict[hash_key] = False
        overlapped_point_by_center_dict[hash_key] = False

        for point_b in point_b_list:
            assert point_a != point_b, "Invalid comparison: crop_info cannot be weed_info."
            if does_square_a_overlap_circle_b(
                point_a=point_a, point_b=point_b
            ):  # True denotes overlapped, while False denotes non-overlapped.
                overlapped_point_by_circle_dict[hash_key] = True
            if does_square_a_overlap_center_b(
                point_a=point_a, point_b=point_b
            ):  # True denotes overlapped, while False denotes non-overlapped.
                overlapped_point_by_center_dict[hash_key] = True

            if overlapped_point_by_circle_dict[hash_key] and overlapped_point_by_center_dict[hash_key]:  # Early break.
                break

    return overlapped_point_by_circle_dict, overlapped_point_by_center_dict


def create_images_overlapped_points_dict(
    points_info_dict: Dict[str, Dict[str, List[Dict[str, Any]]]]
) -> Tuple[Dict[str, Dict[Tuple[float, float, str], bool]], Dict[str, Dict[Tuple[float, float, str], bool]]]:
    overlapped_points_by_circle_dict: Dict[str, Dict[Tuple[float, float, str], bool]] = defaultdict(default_bool_dict)
    overlapped_points_by_center_dict: Dict[str, Dict[Tuple[float, float, str], bool]] = defaultdict(default_bool_dict)
    for image_id, plants_info in points_info_dict.items():
        (
            crop_overlapped_point_by_circle_dict,
            crop_overlapped_point_by_center_dict,
        ) = create_image_overlapped_points_dict(point_a_list=plants_info["crop"], point_b_list=plants_info["weed"])
        (
            weed_overlapped_point_by_circle_dict,
            weed_overlapped_point_by_center_dict,
        ) = create_image_overlapped_points_dict(point_a_list=plants_info["weed"], point_b_list=plants_info["crop"])
        overlapped_points_by_circle_dict[image_id].update(crop_overlapped_point_by_circle_dict)
        overlapped_points_by_circle_dict[image_id].update(weed_overlapped_point_by_circle_dict)
        overlapped_points_by_center_dict[image_id].update(crop_overlapped_point_by_center_dict)
        overlapped_points_by_center_dict[image_id].update(weed_overlapped_point_by_center_dict)

    return overlapped_points_by_circle_dict, overlapped_points_by_center_dict


def get_all_annotations_from_images(path: str, image_ids: Set[str]) -> List[Dict[str, Any]]:
    with open(path) as f:
        file = json.load(f)

    return list(ann for ann in file["annotations"] if ann["image_id"] in image_ids)


def get_embeddings_with_image_id_by_key(
    filename: str, dataset_id: str, key: Optional[str] = None, value: Optional[str] = None
) -> Set[str]:
    dataset_filepath = os.path.join(CARBON_DATA_DIR, f"deeplearning/datasets/{dataset_id}/{filename}")
    with open(dataset_filepath, "r") as f:
        dataset = json.load(f)

    return {f"{item['id']}.pt" for item in dataset["images"] if key is None or item.get(key) == value}


def construct_comparison_data(
    path: str, image_annotation_ids: List[Tuple[str, str]], dataset_version: int = 1,
) -> Tuple[
    Dict[str, str],
    Dict[str, Optional[str]],
    Dict[str, int],
    Dict[str, str],
    List[ComparisonLabels],
    List[str],
    Dict[str, Any],
    Dict[str, Tuple[int, int]],
]:
    annotations_dict = {}
    with open(path) as f:
        if dataset_version == 1:
            file = json.load(f)
        elif dataset_version == 2:
            file = [json.loads(line) for line in f]

    image_ids = set([im[0] for im in image_annotation_ids])
    annotations_ids = set([im[1] for im in image_annotation_ids])

    if dataset_version == 1:
        image_dict = {im["id"]: im for im in file["images"] if im["id"] in image_ids}
    elif dataset_version == 2:
        image_dict = {im["image_id"]: im for im in file if im["image_id"] in image_ids}

    dataset_info_dict = {
        "image_url": {im_id: im_val["uri"] for im_id, im_val in image_dict.items()},
        "image_id_to_geohash": {im_id: im_val["geohash"] for im_id, im_val in image_dict.items()},
        "category": {plant["id"]: plant["name"] for plant in file["categories"]} if dataset_version == 1 else {},
        "captured_at": {im_id: im_val["captured_at"] for im_id, im_val in image_dict.items()},
        "crop_id": {im_id: im_val["crop_id"] for im_id, im_val in image_dict.items()},
        "data_label_to_uuids": {},
        "image_id_to_height_width": {
            im_id: (im_val["height"], im_val["width"]) for im_id, im_val in image_dict.items()
        },
    }

    if dataset_version == 1:
        for a in file["annotations"]:
            if (
                a["annotation_type"] == "point"
                and "id" in a
                and a["image_id"] + "::" + str(a["id"]) in annotations_ids
                and image_within_border(image_data=image_dict[a["image_id"]], x=a["x"], y=a["y"], border=a["radius"])
                and a.get("confidence", 0) == 2
            ):

                a["category_str"] = dataset_info_dict["category"][a["category_id"]]

                annotations_dict[(a["image_id"], a["x"], a["y"])] = a

        annotations = sorted(list(annotations_dict.values()), key=lambda x: x["image_id"])
    elif dataset_version == 2:
        for img in file:
            for a in img["points"]:
                if (
                    img["image_id"] + "::" + str(a["id"]) in annotations_ids
                    and image_within_border(
                        image_data=image_dict[img["image_id"]], x=a["x"], y=a["y"], border=a["radius"]
                    )
                    and a.get("confidence", 0) == 2
                ):

                    a["category_str"] = a["point_category_id"]
                    a["image_id"] = img["image_id"]
                    annotations_dict[(img["image_id"], a["x"], a["y"])] = a

        annotations = sorted(list(annotations_dict.values()), key=lambda x: x["image_id"])

    logging.info(f"Number of annotations total: {len(annotations)}")

    num_pairs = len(annotations) // 2

    comparison_labels = []
    index_ids = []
    timestamp_ms = maka_control_timestamp_ms()
    for init_idx in range(num_pairs):
        idx_1 = 2 * init_idx
        idx_2 = idx_1 + 1
        annotation_1 = annotations[idx_1]
        annotation_2 = annotations[idx_2]

        # Here, we create a fake comparison label out of the deepweed dataset, so the match parameter doesn't matter
        fake_id = f"{timestamp_ms}_{init_idx}"
        label = ComparisonLabels(
            id=fake_id,
            created=idx_1,
            match=False,  # Set it back to False since the ground truth is not available.
            valid=True,
            image_one=annotation_1["image_id"],
            label_one=annotation_1.get("label_id", ""),
            label_one_category=annotation_1["category_str"],
            label_one_x=annotation_1["x"],
            label_one_y=annotation_1["y"],
            label_one_radius=annotation_1["radius"],
            image_two=annotation_2["image_id"],
            label_two=annotation_2.get("label_id", ""),
            label_two_category=annotation_2["category_str"],
            label_two_x=annotation_2["x"],
            label_two_y=annotation_2["y"],
            label_two_radius=annotation_2["radius"],
            user="user",
            source="evaluation",
        )
        index_ids.append(fake_id)
        comparison_labels.append(label)
        dataset_info_dict["data_label_to_uuids"][fake_id] = {
            "label_one_uuid": get_globally_unique_id(annotation_1),
            "label_two_uuid": get_globally_unique_id(annotation_2),
        }

    return (
        dataset_info_dict["image_url"],
        dataset_info_dict["image_id_to_geohash"],
        dataset_info_dict["captured_at"],
        dataset_info_dict["crop_id"],
        comparison_labels,
        index_ids,
        dataset_info_dict["data_label_to_uuids"],
        dataset_info_dict["image_id_to_height_width"],
    )


def save_image_level_embedding_files_to_torch(
    embeddings_data: Dict[str, List[Tuple[Dict[str, Any], List[Any]]]], dir: str
) -> List[str]:
    filepaths = []
    for image_id, embedding_data_tuples in embeddings_data.items():
        filepath = os.path.join(dir, f"{image_id}.pt")
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        embedding_data = [item[0] for item in embedding_data_tuples]
        embedding_list = [item[1] for item in embedding_data_tuples]

        image_meta = {
            "image_id": embedding_data[0].get("image_id"),
            "captured_at": embedding_data[0].get("captured_at"),
            "label_id": embedding_data[0].get("label_id"),
            "image_url": embedding_data[0].get("image_url"),
            "model_id": embedding_data[0].get("model_id"),
            "geohash": embedding_data[0].get("geohash"),
            "epoch": embedding_data[0].get("epoch"),
            "crop_id": embedding_data[0].get("crop_id"),
        }

        for key in image_meta.keys():
            for item in embedding_data:
                if key in item:
                    del item[key]
        torch.save(
            {
                "image_meta": image_meta,
                "embeddings_data": embedding_data,
                "embeddings": torch.tensor(embedding_list).half(),
            },
            filepath,
        )
        filepaths.append(filepath)

    return filepaths


def save_image_level_embedding_files_to_hdf5(
    embedding_dataset_config: Dict[str, Any], embeddings_data: Dict[str, List[Tuple[Dict[str, Any], List[float]]]],
) -> None:
    def create_embedding_dataset(
        model_id: str, embedding_size: int, base_dir: str, dataset_id: str, version_id: str
    ) -> EmbeddingDataset:
        embeddings_dataset_metadata = EmbeddingDatasetMetadata(
            model_id=model_id, embedding_size=embedding_size, dataset_id=dataset_id, version_id=version_id
        )
        embedding_output_path = os.path.join(base_dir, SAVED_H5_FILENAME)
        embedding_dataset = EmbeddingDataset(
            dataset_filepath=embedding_output_path, metadata=embeddings_dataset_metadata,
        )

        return embedding_dataset

    embedding_dataset = create_embedding_dataset(
        model_id=embedding_dataset_config["model_id"],
        embedding_size=embedding_dataset_config["embedding_size"],
        base_dir=embedding_dataset_config["base_dir"],
        dataset_id=embedding_dataset_config["dataset_id"],
        version_id=str(uuid.uuid4()),
    )

    for _, embedding_data_tuples in embeddings_data.items():
        embedding_data = [item[0] for item in embedding_data_tuples]
        embedding_list = [item[1] for item in embedding_data_tuples]

        for data, embedding in zip(embedding_data, embedding_list):
            embedding_datapoint_metadata = EmbeddingDatapointMetadata(
                image_id=data["image_id"],
                image_url=data["image_url"],
                captured_at=data["captured_at"],
                geohash=data["geohash"],
                point_category_id=data["category"],
                label_id=data["label_id"],
                x=data["x"],
                y=data["y"],
                image_crop_id=data["crop_id"],
                point_id=data["uuid"],
            )
            embedding_datapoint = EmbeddingDatapoint(
                embedding=torch.tensor(embedding), metadata=embedding_datapoint_metadata
            )
            embedding_dataset.append(embedding_datapoint)


def save_comparison_embeddings(
    embeddings: List[ComparisonEmbeddingObject],
    dir: str,
    id_to_url: Dict[str, str],
    captured_at_dict: Dict[str, int],
    crop_id_dict: Dict[str, str],
    model_id: str,
    dataset_id: str,
    mode: str = "hdf5",
) -> None:

    embeddings_data = defaultdict(list)
    for embedding in embeddings:
        embedding_dictionary = embedding.to_dict()

        dictionary = {
            "image_id": embedding_dictionary["image_id"],
            "x": embedding_dictionary["x"],
            "y": embedding_dictionary["y"],
            "radius": embedding_dictionary["radius"],
            "category": embedding_dictionary["category"],
            "label_id": embedding_dictionary["label_id"],
            "epoch": embedding_dictionary["epoch"],
            "image_url": id_to_url[embedding_dictionary["image_id"]],
            "model_id": model_id,
            "geohash": embedding_dictionary["geohash"],
            "captured_at": captured_at_dict[embedding_dictionary["image_id"]],
            "crop_id": crop_id_dict[embedding_dictionary["image_id"]],
            "uuid": embedding_dictionary["uuid"],
        }
        embeddings_data[embedding_dictionary["image_id"]].append((dictionary, embedding_dictionary["embedding"]))

    if mode == "hdf5":
        save_image_level_embedding_files_to_hdf5(
            embedding_dataset_config={
                "model_id": model_id,
                "embedding_size": len(embedding_dictionary["embedding"]),
                "base_dir": dir,
                "dataset_id": dataset_id,
            },
            embeddings_data=embeddings_data,
        )
    elif mode == "torch":
        save_image_level_embedding_files_to_torch(embeddings_data=embeddings_data, dir=dir)


def load_embeddings_from_torch(input: Any) -> Dict[str, Any]:
    data = torch.load(input)
    return {
        "embeddings_data": data["embeddings_data"],
        "embeddings": data["embeddings"],
        "image_meta": data["image_meta"],
    }


def cosine_similarity_normed_inputs(normalized_query: torch.Tensor, normalized_db: torch.Tensor) -> torch.Tensor:
    # Assumes inputs are normalized has been normalized
    assert (
        normalized_query.shape[1] == normalized_db.shape[1]
    ), f"Size mismatch: {normalized_query.shape}, {normalized_db.shape}"
    cosine_similarity = torch.matmul(normalized_query, normalized_db.t())
    return cosine_similarity


def get_comparison_evaluation_dir() -> str:
    return f"{CARBON_DATA_DIR}/deeplearning/comparison_evaluations"


def download_comparison_files_for_dataframe(json_filepath: str, comparison_model_id: str) -> None:
    image_ids = extract_image_ids_from_dataset(json_filepath)

    image_filenames = set([f"{image_id}.pt" for image_id in image_ids])

    download_records(
        comparison_model_id,
        bucket=get_comparison_evaluation_bucket(),
        filename_filters=image_filenames,
        s3_directory=f"{Environment.PRODUCTION.name.lower()}/comparison_evaluations",
        save_dir=f"{get_comparison_evaluation_dir()}/{comparison_model_id}",
        only_use_basename_in_download=True,
        skip_existing_files=True,
    )


def generate_samples_outside_of_size_limit(
    n: int, size_multiple: float, comparison_labels: List[ComparisonLabels]
) -> List[ComparisonLabels]:
    new_comparison_labels: List[ComparisonLabels] = []
    while len(new_comparison_labels) < n:
        random_pair = random.sample(comparison_labels, k=2)
        item_a = random_pair[0].label_one_radius
        item_b = random_pair[1].label_one_radius

        min_size = item_a / size_multiple
        max_size = item_a * size_multiple

        if item_b < min_size or item_b > max_size:
            new_comparison_labels.append(
                ComparisonLabels(
                    id=str(uuid.uuid4()),
                    created=0,
                    match=False,  # Anything outside this limit should be False
                    valid=True,
                    image_one=random_pair[0].image_one,
                    label_one=random_pair[0].label_one,
                    label_one_category=random_pair[0].label_one_category,
                    label_one_x=random_pair[0].label_one_x,
                    label_one_y=random_pair[0].label_one_y,
                    label_one_radius=random_pair[0].label_one_radius,
                    image_two=random_pair[1].image_one,
                    label_two=random_pair[1].label_one,
                    label_two_category=random_pair[1].label_one_category,
                    label_two_x=random_pair[1].label_one_x,
                    label_two_y=random_pair[1].label_one_y,
                    label_two_radius=random_pair[1].label_one_radius,
                    source=DATASOURCE_SIZE_DIFFERENTIATED_GENERATED,
                    user="<EMAIL>",
                )
            )
    return new_comparison_labels
