from dataclasses import dataclass, field
from typing import Dict, Optional

from dataclass_wizard import JSON<PERSON><PERSON>rd

from deeplearning.comparison.constants import (
    DATASOURCE_GENERATED,
    DATASOURCE_MANUAL,
    DATASOURCE_PRIORITIZED,
    DATASOURCE_SAMPLED,
)
from deeplearning.utils.use_cases import ModelUseCase
from deeplearning.utils.wandb import WANDB_PROJECT_MAP


@dataclass(eq=True, frozen=True)
class ComparisonConfig(JSONWizard):
    wandb_project: str = WANDB_PROJECT_MAP[ModelUseCase.COMPARISON]

    make_trt_model: bool = True
    ci_run: bool = False
    convert_int8: bool = False
    source_weights: Dict[str, float] = field(
        default_factory=lambda: {
            DATASOURCE_SAMPLED: 0.9,
            DATASOURCE_GENERATED: 0.1,
            DATASOURCE_PRIORITIZED: 0.1,
            DATASOURCE_MANUAL: 0.1,
        }
    )
    source_sampler: str = "ComparisonLabelSamplerCategory"
    reference_model_for_dataset: Optional[str] = None

    db_filter_start_time_ms: Optional[int] = None
    db_filter_end_time_ms: Optional[int] = None

    db_filter_max_size_multiplier: Optional[float] = 1.35

    label_generation_size_based_enabled: bool = True
    label_generation_size_based_multiplier: float = 2.0
    label_generation_size_based_n: int = 300000
