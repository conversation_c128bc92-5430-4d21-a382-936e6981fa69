import argparse
import ast
import datetime
import functools
import logging
import os
from typing import Callable, Dict, Type
from uuid import uuid4

import pandas as pd
import torch
from torch.distributed.elastic.multiprocessing.errors import record

from deeplearning.comparison.config import ComparisonConfig
from deeplearning.comparison.data_utils import download_comparison_data, get_comparison_evaluation_prefix, split_labels
from deeplearning.comparison.loss import (
    binary_cross_entropy_punish_false_positives,
    contrastive_loss,
    mse_loss_punish_false_positives,
)
from deeplearning.comparison.models_dict import MODELS_DICT
from deeplearning.comparison.sampler import (
    ComparisonLabelSampler,
    ComparisonLabelSamplerBasic,
    ComparisonLabelSamplerCategory,
    ComparisonLabelSamplerHardExample,
    ComparisonLabelSamplerSizeGeo,
    ComparisonLabelSamplerSource,
    ComparisonLabelSamplerUniform,
)
from deeplearning.comparison.trainer import ComparisonTrainer, LrAdjuster
from deeplearning.comparison.trt_convert import MAX_BATCH_SIZE
from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.scripts.comparison.constants import DATA_S3_KEY_PREFIX
from deeplearning.scripts.utils.utils import add_job_creator_arguments, generate_model_id
from deeplearning.utils.download_utils import download_records
from deeplearning.utils.tensor import broadcast_object
from deeplearning.utils.trainer import Environment, upload_directory

LOG = logging.getLogger(__name__)


LOSS_FN_DICT: Dict[str, Callable[[torch.Tensor, torch.Tensor], torch.Tensor]] = {
    "binary_cross_entropy": torch.nn.functional.binary_cross_entropy,
    "l1_loss": torch.nn.functional.l1_loss,
    "mse_loss": torch.nn.functional.mse_loss,
    "binary_cross_entropy_punish_false_positives": binary_cross_entropy_punish_false_positives,
    "mse_loss_punish_false_positives": mse_loss_punish_false_positives,
    "contrastive_loss": contrastive_loss,
}

SAMPLERS: Dict[str, Type[ComparisonLabelSampler]] = {
    "ComparisonLabelSamplerBasic": ComparisonLabelSamplerBasic,
    "ComparisonLabelSamplerSizeGeo": ComparisonLabelSamplerSizeGeo,
    "ComparisonLabelSamplerCategory": ComparisonLabelSamplerCategory,
    "ComparisonLabelSamplerHardExample": ComparisonLabelSamplerHardExample,
    "ComparisonLabelSamplerSource": ComparisonLabelSamplerSource,
    "ComparisonLabelSamplerUniform": ComparisonLabelSamplerUniform,
}


@record
def main() -> None:  # noqa: C901
    parser = argparse.ArgumentParser()
    parser.add_argument("--pretrained-model", type=str, default=None)
    parser.add_argument("--model-id", type=str, default=None)
    parser.add_argument("--dataset-id", type=str, default=None)
    parser.add_argument("--data-s3-key-prefix", type=str, default=DATA_S3_KEY_PREFIX)
    parser.add_argument("--initial-lr", type=float, default=0.005)
    parser.add_argument("--lr-schedule", type=str, default="20,40,60")
    parser.add_argument("--lr-gamma", type=float, default=0.5)
    parser.add_argument("--fast-run", action="store_true", default=False)
    parser.add_argument(
        "--model",
        choices=list(MODELS_DICT.keys()),
        default="CoordConvLearnableReductionDensenet201CosineComparisonModel",
    )
    parser.add_argument("--loss-fn", choices=list(LOSS_FN_DICT.keys()), default="mse_loss")
    parser.add_argument("--mask-around-plant", action="store_true", default=False)
    parser.add_argument("--train-batch-size", type=int, default=8)
    parser.add_argument("--val-batch-size", type=int, default=8)
    parser.add_argument("--data-pipeline-processes", type=int, default=4)
    parser.add_argument("--resume-from-model", type=str, default=None)
    parser.add_argument("--epochs", type=int, default=40)
    parser.add_argument("--num-balanced-examples", type=int, default=480000)
    parser.add_argument(
        "--sampler", default="ComparisonLabelSamplerCategory", choices=list(SAMPLERS.keys()),
    )
    parser.add_argument("--extra-augmentations", action="store_true", default=False)
    parser.add_argument(
        "--lr-adjuster", type=str, choices=["multi_step", "cosine_annealing"], default="cosine_annealing"
    )
    parser.add_argument("--lr-eta-min", type=float, default=0.0)
    parser.add_argument("--lr-t-max", type=int, default=40)
    parser.add_argument("--only-verified-data", action="store_true")
    parser.add_argument("--no-only-verified-data", action="store_false", dest="only_verified_data")
    parser.add_argument(
        "--threshold", type=float, default=0.5, help="Threshold for out_hat to be considered match vs nonmatch"
    )
    parser.add_argument("--loss-arguments", type=ast.literal_eval, default={})

    add_job_creator_arguments(parser)
    parser.set_defaults(only_verified_data=True)

    args = parser.parse_args()

    model = MODELS_DICT[args.model]
    loss_fn = functools.partial(LOSS_FN_DICT[args.loss_fn], **args.loss_arguments)

    dl_config_dict = {**args.dl_config}
    if args.fast_run:
        dl_config_dict["wandb_project"] = "comparison-fast-run"
        dl_config_dict["label_generation_size_based_n"] = 64
    dl_config = ComparisonConfig.from_dict(dl_config_dict)

    trainer = ComparisonTrainer()

    if torch.distributed.get_rank() == 0:
        if args.resume_from_model is not None:
            download_records(args.resume_from_model)

    torch.distributed.barrier()

    tags = []
    if args.pretrained_model:
        tags.append("pretrained")

    config = {}
    if args.pretrained_model is not None:
        config["pretrained_model_id"] = args.pretrained_model

    image_id_to_url = None
    db_path = None
    label_splits = None
    image_id_to_geo = None
    image_id_to_height_width = None

    model_id = None
    data_id = None
    description = None

    if torch.distributed.get_rank() == 0:
        if args.dataset_id is None:
            data_id = str(uuid4())
        else:
            data_id = args.dataset_id
        assert data_id is not None
        data_dir = os.path.join(f"{CARBON_DATA_DIR}/deeplearning/comparison_data", data_id)
        image_id_to_url, labels, db_path, image_id_to_geo, image_id_to_height_width = download_comparison_data(
            s3_key_prefix=args.data_s3_key_prefix,
            data_dir=data_dir,
            only_verified_data=args.only_verified_data,
            fast_run=args.fast_run,
            comparison_config=dl_config,
        )

        if dl_config.reference_model_for_dataset is not None:  # Download the previous split csv files.
            download_records(item_id=dl_config.reference_model_for_dataset, s3_directory="models")
            dataset_split_path = os.path.join(
                CARBON_DATA_DIR, f"deeplearning/models/{dl_config.reference_model_for_dataset}"
            )

            label_splits = {}
            for phase in ["train", "validation", "test"]:
                split_filepath = os.path.join(dataset_split_path, f"{phase}.csv")
                read_df = pd.read_csv(split_filepath)
                label_splits[phase] = [row.id for row in read_df.itertuples(index=False)]

                LOG.info(f"Load comparison dataset: {split_filepath}.")
        else:  # Create a new label splits.
            label_splits = split_labels(labels=labels, train_split_percentage=0.7, validation_split_percentage=0.05)

        logging.info(f"Number of labels: {len(labels)}")

        if args.job_id is None:
            model_id = generate_model_id()
        else:
            model_id = args.job_id

        if args.description is None:
            description = f"(comparison) Development run {datetime.datetime.now()}"
        else:
            description = args.description

    image_id_to_url = broadcast_object(image_id_to_url)
    image_id_to_height_width = broadcast_object(image_id_to_height_width)
    image_id_to_geo = broadcast_object(image_id_to_geo)
    db_path = broadcast_object(db_path)
    label_splits = broadcast_object(label_splits)

    model_id = broadcast_object(model_id)
    description = broadcast_object(description)
    data_id = broadcast_object(data_id)
    assert model_id is not None
    assert label_splits is not None
    assert db_path is not None
    assert image_id_to_url is not None
    assert image_id_to_geo is not None
    assert image_id_to_height_width is not None

    config["dataset"] = {
        "train": len(label_splits["train"]),
        "validation": len(label_splits["validation"]),
        "test": len(label_splits["test"]),
        "dataset_id": data_id,
    }

    num_balanced_examples = args.num_balanced_examples
    epochs = args.epochs

    if args.fast_run:
        num_balanced_examples = 100
        epochs = 1

    trainer.explicit_split_dataset(
        label_dataset_path=db_path,
        model_dir=f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}",
        image_id_to_url=image_id_to_url,
        image_id_to_geohash=image_id_to_geo,
        train_ids_in_dataset=label_splits["train"],
        validation_ids_in_dataset=label_splits["validation"],
        test_ids_in_dataset=label_splits["test"],
        source_weights=dl_config.source_weights,
        source_sampler=SAMPLERS[dl_config.source_sampler],
        num_balanced_examples=num_balanced_examples,
        mask_around_plant=args.mask_around_plant,
        sampler=SAMPLERS[args.sampler],
        extra_augmentations=args.extra_augmentations,
        crop_on_server=True,
        image_id_to_height_width=image_id_to_height_width,
    )

    lr_schedule = [int(i) for i in args.lr_schedule.split(",")]

    lr_adjuster = LrAdjuster.MultiStep if args.lr_adjuster == "multi_step" else LrAdjuster.CosineAnnealing
    lr_t_max = epochs if args.lr_t_max is None else args.lr_t_max

    if args.preview and not args.fast_run:
        environment = Environment.PREVIEW
    elif args.production and not args.fast_run:
        environment = Environment.PRODUCTION
    else:
        environment = Environment.DEVELOPMENT

    try:
        trainer.train(
            config=dl_config,
            checkpoint_dir=f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}",
            description=description,
            deploy=environment == Environment.PRODUCTION,
            environment=environment,
            fast_run=args.fast_run,
            initial_lr=args.initial_lr,
            train_batch_size=args.train_batch_size,
            val_batch_size=args.val_batch_size,
            test_batch_size=MAX_BATCH_SIZE,
            epochs=epochs,
            lr_milestones=lr_schedule,
            lr_gamma=args.lr_gamma,
            model=model,
            loss_fn=loss_fn,
            data_pipeline_processes=args.data_pipeline_processes,
            resume_from=f"{CARBON_DATA_DIR}/deeplearning/models/{args.resume_from_model}/ckpt/latest.ckpt"
            if args.resume_from_model is not None
            else None,
            save_embeddings_in_db=True,
            embedding_output_path=os.path.join(
                f"{CARBON_DATA_DIR}/deeplearning/models/", get_comparison_evaluation_prefix(model_id, environment),
            ),
            image_id_to_url=image_id_to_url,
            model_id=model_id,
            additional_wandb_config=config,
            lr_adjuster=lr_adjuster,
            lr_eta_min=args.lr_eta_min,
            lr_t_max=lr_t_max,
            threshold=args.threshold,
        )
    except Exception as e:
        LOG.info(f"Exception running job on rank {torch.distributed.get_rank()}: {e}")

        if torch.distributed.get_rank() == 0 and os.path.exists(f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}"):
            upload_directory(source_dir=f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}")

        raise e


if __name__ == "__main__":
    main()
