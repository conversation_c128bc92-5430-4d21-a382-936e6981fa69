import asyncio
from contextlib import AsyncExitStack, asynccontextmanager
from dataclasses import fields
from enum import Enum
from time import time
from typing import Any, AsyncIterator, Callable, Coroutine, Iterable, List, Optional, Tuple

import generated.proto.rtc.jobs_pb2 as jobs_pb
from config.client.cpp.config_client_python import ConfigTree
from generated.proto.geo import geo_pb2
from hw_client_gps_distributor.python.hw_client_gps_distributor import get_next_non_opt
from lib.cloud.auth import TokenStore
from lib.common.asyncio.linked_event import linked_event
from lib.common.asyncio.simple_task_group import SimpleTaskGroup
from lib.common.asyncio.task_master import TASK_TYPE
from lib.common.config.accessor import TypedConfigAccessor
from lib.common.geo.geojson import Feature, LineString
from lib.common.logging import get_logger
from lib.common.redis_client import RedisClient
from lib.common.units.angle import Angle
from lib.common.units.distance import Distance
from lib.common.units.speed import ZERO_SPEED, Speed
from lib.drivers.errors.error import CarbonDeviceCommTimeoutException
from lib.rtc.farm_zone_data import FarmAreas, TractorBoundaryState
from lib.rtc.jobs.client import InterventionCause, JobsServiceClient
from lib.rtc.jobs.constants import ACTIVE_TASK_KEY
from tractor_ctl.atim_server import AtimController
from tractor_ctl.autonomy.furrow_follow_estimator import BadDetectionError, FurrowFollowEstimator
from tractor_ctl.autonomy.gps_steering_estimator import GpsSteeringEstimator, PathCompleteException
from tractor_ctl.autonomy.path_checker import HeadingDeltaException, OnPathState, PathChecker
from tractor_ctl.autonomy.task_state_validator import GearMap, StateWithData, TaskStateDetails, TaskStateValidator
from tractor_ctl.autonomy.task_wrapper import TaskType, TaskWrapper
from tractor_ctl.event_bus import ATIM_STATE_CHANGE, TRACTOR_EVENT_BUS
from tractor_ctl.state.global_states import ACTIVE_FARM, TRACTOR_BOUNDARY_STATE
from tractor_ctl.tractor_if import Gear, TractorIF

LOG = get_logger(__name__)

AUTONOMOUS_TASK_RUNNER_STOPPED = "AUTONOMOUS_TASK_RUNNER_STOPPED"
LAST_POS_UPDATE_DELTA_S = 1.0


class TaskState(Enum):
    TASK_STATE_UNSPECIFIED = jobs_pb.STATE_UNSPECIFIED
    PENDING = jobs_pb.PENDING
    IN_PROGRESS = jobs_pb.IN_PROGRESS
    COMPLETED = jobs_pb.COMPLETED
    CANCELLED = jobs_pb.CANCELLED


class _GPSPathProvider:
    def __init__(self) -> None:
        self._path = Feature(geometry=LineString(coordinates=[]))

        def __no_op() -> None:
            pass

        self._cb = __no_op

    def set_path(self, grpc_ls: geo_pb2.LineString) -> None:
        self._path = Feature(geometry=LineString(coordinates=[[pt.lng, pt.lat] for pt in grpc_ls.points]))
        self._cb()

    def get_current_path(self) -> Optional[Feature]:
        return self._path

    def on_path_change(self, cb: Callable[[], None]) -> None:
        self._cb = cb


class AutonomousTaskRunner:
    def __init__(
        self,
        tractor: TractorIF,
        atim: AtimController,
        ts: TokenStore,
        tree: ConfigTree,
        min_steering_speed: TypedConfigAccessor[float, Speed],
    ) -> None:
        self._tractor = tractor
        self._atim = atim
        self._disabled = asyncio.Event()
        self._min_steering_speed = min_steering_speed
        self._reload_active_task = asyncio.Event()
        self._active_task: Optional[TaskWrapper] = None
        self._aio_task: Optional[TASK_TYPE] = None
        self._job_client = JobsServiceClient(ts)
        self._validator = TaskStateValidator(tractor, self._job_client, tree)
        self._gps_path_provider = _GPSPathProvider()
        TRACTOR_BOUNDARY_STATE.watch(self._on_boundary_update)

    @staticmethod
    async def build(
        tractor: TractorIF,
        atim: AtimController,
        ts: TokenStore,
        tree: ConfigTree,
        min_steering_speed: TypedConfigAccessor[float, Speed],
    ) -> "AutonomousTaskRunner":
        t = AutonomousTaskRunner(tractor, atim, ts, tree, min_steering_speed)
        await t.__ainit(tree)
        return t

    async def __ainit(self, tree: ConfigTree) -> None:
        self._redis = await RedisClient.build()
        self._lock = asyncio.Lock()
        self._gps_steering_est = await GpsSteeringEstimator.build(
            tree.get_node("gps_path_follow"), self._gps_path_provider, self._tractor
        )
        self._ff_est = FurrowFollowEstimator(tree.get_node("furrow_following"), self._redis, self._tractor)

    async def enable(self) -> None:
        if self._aio_task is not None:
            await self._aio_task
        self._disabled.clear()
        self._aio_task = asyncio.get_event_loop().create_task(self._loop())

    async def disable(self, cause: InterventionCause = InterventionCause.UNSPECIFIED) -> None:
        task_id = 0
        if self._active_task is not None:
            task = self._active_task.get_active()
            if task is not None:
                task_id = task.id
        await self.__intervention_req(task_id, True, "", "Autonomy was canceled", cause, False)

    async def _loop(self) -> None:
        if TRACTOR_BOUNDARY_STATE.current.valid:
            await self._do_boundary_check(TRACTOR_BOUNDARY_STATE.current, ACTIVE_FARM.current)
        self._reload_active_task.set()
        while not self._disabled.is_set():
            try:
                await self.__control()
            except BaseException:
                LOG.exception("Unknown exception in task sequence")

    async def __validate_task(self, task: jobs_pb.Task) -> Optional[str]:
        if task.state in [jobs_pb.CANCELLED, jobs_pb.COMPLETED]:
            return f"Task {task.id} is in an invalid state to start. state={task.state}"
        task_type = task.WhichOneof("task_details")
        if task_type not in [
            TaskType.tractor_state,
            TaskType.follow_path,
            TaskType.laser_weed,
            TaskType.go_to_reversible_path,
            TaskType.sequence,
        ]:
            return f"Task {task.id} is an unsupported task type {task_type}"
        return None

    async def __intervention_req(
        self,
        task_id: int,
        set_failed: bool,
        qualification: str,
        description: str,
        cause: InterventionCause = InterventionCause.TRACTOR_REQUEST,
        emit: bool = True,
    ) -> None:
        async with self._lock:
            send_to_cloud = not self._disabled.is_set()
            self._disabled.set()
            if emit:
                await self._stopped()
            await self.__clear_active_task()
            if send_to_cloud:
                if set_failed and task_id != 0:
                    await self._job_client.set_task_failed(task_id)
                await self._job_client.create_intervention(task_id, qualification, description, cause)
            else:
                LOG.info(f"Not sending intervention for {task_id} description={description}")

    async def __control(self) -> None:
        if self._reload_active_task.is_set():
            self._reload_active_task.clear()
            await self.__get_active_task()
        if self._active_task is None:
            await self.__intervention_req(0, False, "", "No active task defined, no autonomy to perform.")
            return
        task = self._active_task.get_active()
        if task is None:
            # This sequence is complete time to get new one
            self._reload_active_task.set()
            await self.__clear_active_task()
            return
        err_msg = await self.__validate_task(task)
        if err_msg is not None:
            # TODO should I mark this task as failed?
            await self.__intervention_req(task.id, False, "", err_msg)
            return
        await self._redis.set(ACTIVE_TASK_KEY, task.id)
        task_type = task.WhichOneof("task_details")
        if task_type == TaskType.tractor_state:
            await self.__set_tractor_state(task)
        elif task_type == TaskType.follow_path:
            await self.__follow_path(task)
        elif task_type == TaskType.laser_weed:
            await self.__laser_weed(task)
        elif task_type == TaskType.go_to_reversible_path:
            await self.__reversible_path(task)
        elif task_type == TaskType.go_to_and_face:
            await self.__go_2_and_face(task)
        elif task_type == TaskType.sequence:
            await self.__set_task_complete(task.id)  # nothing needed for sequence tasks we did all the children

    async def __clear_active_task(self) -> None:
        self._active_task = None
        await self._redis.set(ACTIVE_TASK_KEY, 0)

    async def __get_active_task(self) -> None:
        task = await self._job_client.get_active_task()
        if task is None:
            LOG.info("No active task found")
            return
        LOG.info(f"Loaded task {task.id} ({task.name})")
        self._active_task = TaskWrapper(task)

    async def __set_tractor_state(self, task: jobs_pb.Task) -> None:
        await self._job_client.set_task_in_progress(task.id)
        for state in task.tractor_state.state:
            if state.WhichOneof("state") == "gear":
                if state.gear not in GearMap:
                    await self.__intervention_req(task.id, True, "", "Unsupported gear state")
                    return
                else:
                    expected = GearMap[state.gear]
                    if not await self.__set_gear(expected, self._validator.cfg.state_change_max_attempts):
                        await self.__intervention_req(task.id, True, "", f"Failed to set gear to {expected.name}")
                        return
            elif state.WhichOneof("state") == "hitch":
                # Only support command not arbitrary value currently
                if state.hitch.command == jobs_pb.HitchState.HITCH_COMMAND_UNSPECIFIED:
                    await self.__intervention_req(task.id, True, "", "Unsupported hitch state")
                    return
                elif state.hitch.command == jobs_pb.HitchState.RAISED:
                    if not await self.__raise_hitch(self._validator.cfg.state_change_max_attempts):
                        await self.__intervention_req(task.id, True, "", "Failed to raise hitch")
                        return
                elif state.hitch.command == jobs_pb.HitchState.LOWERED:
                    if not await self.__lower_hitch(self._validator.cfg.state_change_max_attempts):
                        await self.__intervention_req(task.id, True, "", "Failed to lower hitch")
                        return
            else:
                LOG.warning("Unknown state type")
                await self.__intervention_req(task.id, True, "", "Unsupported tractor state request")

        await self.__set_task_complete(task.id)

    @asynccontextmanager
    async def __set_lw_state(self, weeding: bool, thinning: bool) -> AsyncIterator[bool]:
        try:
            success = await self._atim.set_lw_action_state(weeding, thinning)
            yield success
        finally:
            await self._atim.set_lw_action_state(False, False)

    async def __laser_weed(self, task: jobs_pb.Task) -> None:  # noqa: C901
        await self._ff_est.reset()
        async with linked_event(self._disabled) as stop_event:
            try:
                pc = await PathChecker.build(
                    Distance.from_meters(task.laser_weed.tolerances.continuous_crosstrack),
                    Distance.from_meters(task.laser_weed.tolerances.crosstrack),
                    Distance.from_meters(task.laser_weed.tolerances.distance),
                    Angle.from_degrees(task.laser_weed.tolerances.heading),
                    task.laser_weed.path,
                    task.laser_weed.path_is_reversible,
                )
            except HeadingDeltaException:
                await self.__intervention_req(task.id, False, "", "Invalid tractor heading")
                return
            if not await pc.validate_start_pos():
                LOG.info("robot not in valid starting point")
                await self.__intervention_req(task.id, False, "", "Invalid tractor position")
                return
            valid_state, enforcers = await self.__expected_state_handler(
                task.id, task.expected_tractor_state, stop_event
            )
            if not valid_state:
                LOG.info("Invalid state detected, cannot furrow follow path")
                await self.__intervention_req(task.id, False, "", "Invalid tractor state")
                for enforcer in enforcers:
                    await enforcer
                return
            enforcers.append(self.__path_checker_enforcer(task.id, pc, stop_event))
            LOG.info("Starting furrow follow")
            await self._job_client.set_task_in_progress(task.id)
            if not await self._atim.set_enable_from_autonomy(True):
                LOG.info("Failed to enable atim for speed control")
                await self.__intervention_req(task.id, True, "", "Could not enable ATIM")
                for enforcer in enforcers:
                    await enforcer
                return
            enforcers.append(self.__atim_disable_watcher(task.id, stop_event))
            enforcers.append(self.__pos_updater(task.id, stop_event))
            async with AsyncExitStack() as es:
                tg = await es.enter_async_context(SimpleTaskGroup())
                for enforcer in enforcers:
                    await tg.create_task(enforcer)
                lw_state_set = await es.enter_async_context(
                    self.__set_lw_state(task.laser_weed.weeding_enabled, task.laser_weed.thinning_enabled)
                )
                if not lw_state_set:
                    await self.__intervention_req(task.id, True, "", "Could not set laser weeder state")
                    return
                while not stop_event.is_set():
                    try:
                        angle = await self._ff_est.get_target_angle()
                        if angle is not None:
                            if self._min_steering_speed.value.mph < 0.0 or (
                                self._tractor.get_speed() > self._min_steering_speed.value
                            ):
                                await self._tractor.set_wheel_angle(angle)
                    except BadDetectionError as ex:
                        LOG.info(f"Furrow following failed err: {ex}")
                        stop_event.set()
                        await self.__intervention_req(task.id, True, "", str(ex))
                        return
                    except CarbonDeviceCommTimeoutException:
                        LOG.info("Failed to communicate with HW board")
                        stop_event.set()
                        await self.__intervention_req(
                            task.id, True, "", "Failed to complete path, due to HW communication failure"
                        )
                        return
                    except Exception:
                        LOG.exception("Unknown error while furrow following.")
                        stop_event.set()
                        await self.__intervention_req(task.id, True, "", "Failed to complete path")
                        return
            if pc.last_state == OnPathState.PATH_COMPLETE:
                await self.__set_task_complete(task.id)
                await self._tractor.set_speed(ZERO_SPEED())  # stop moving here for next command

    async def __reversible_path(self, task: jobs_pb.Task) -> None:
        task_state = await self._validator.validate_reversible_path_complete(task)
        await self.__check_manual_move_task(task, task_state)

    async def __go_2_and_face(self, task: jobs_pb.Task) -> None:
        task_state = await self._validator.validate_go_2_and_face_complete(task)
        await self.__check_manual_move_task(task, task_state)

    async def __check_manual_move_task(self, task: jobs_pb.Task, task_state: TaskStateDetails) -> None:
        if task_state.unsupported:
            await self.__intervention_req(
                task.id, False, "", "Unsupported task",
            )
        else:
            failed_states = []
            for field in fields(task_state):
                if field.name == "unsupported":
                    continue
                state: Optional[StateWithData[Any]] = getattr(task_state, field.name)
                if state is not None and not state.state:
                    failed_states.append(field.name)
            if failed_states:
                await self.__intervention_req(
                    task.id,
                    False,
                    "",
                    f"Tractor is not close enough to automatically close task. Failed completion criteria = [{', '.join(failed_states)}]",
                )
            else:
                # We are close enough to automatically mark task complete
                await self.__set_task_complete(task.id)

    async def __follow_path(self, task: jobs_pb.Task) -> None:
        if task.follow_path.speed.WhichOneof("speed") != "constant_mph":
            await self.__intervention_req(task.id, True, "", "Unsupported speed control for path follow")
            return
        await self._job_client.set_task_in_progress(task.id)
        self._gps_path_provider.set_path(task.follow_path.path)
        await self._gps_steering_est.reset()
        async with linked_event(self._disabled) as stop_event:
            valid_state, enforcers = await self.__expected_state_handler(
                task.id, task.expected_tractor_state, stop_event
            )
            if not valid_state:
                LOG.info("Invalid state detected, cannot follow path")
                stop_event.set()
                await self.__intervention_req(task.id, True, "", "Invalid tractor state")
                for enforcer in enforcers:
                    await enforcer
                return
            enforcers.append(self.__pos_updater(task.id, stop_event))
            LOG.info(
                f"Starting path should take {task.expected_duration.seconds} seconds traveling at {task.follow_path.speed.constant_mph} mph"
            )
            async with SimpleTaskGroup() as tg:
                for enforcer in enforcers:
                    await tg.create_task(enforcer)
                await self._tractor.set_speed(Speed.from_mph(task.follow_path.speed.constant_mph))
                while not stop_event.is_set():
                    try:
                        angle = await self._gps_steering_est.get_target_angle()
                        if angle is not None:
                            if self._min_steering_speed.value.mph < 0.0 or (
                                self._tractor.get_speed() > self._min_steering_speed.value
                            ):
                                await self._tractor.set_wheel_angle(angle)
                    except PathCompleteException:
                        stop_event.set()
                        if task.follow_path.stop_on_completion:
                            await self._tractor.set_speed(ZERO_SPEED())
                        await self.__set_task_complete(task.id)
                        return
                    except Exception:
                        LOG.exception("task autonomy failed.")
                        stop_event.set()
                        await self.__intervention_req(task.id, True, "", "Failed to complete path")
                        return

    async def __set_task_complete(self, task_id: int) -> None:
        if self._active_task is not None:
            self._active_task.complete()
        await self._job_client.set_task_complete(task_id)

    async def __raise_hitch(self, attempts: int = 1) -> bool:
        LOG.info("Raising hitch")
        for _ in range(attempts):
            try:
                await self._tractor.raise_hitch()
            except Exception:
                # HACK because board always times out for hitch change
                pass
            await asyncio.sleep(self._validator.cfg.hitch_change_time)
            if self._tractor.get_hitch_percent() > self._validator.cfg.hitch_raised_min:
                return True
        LOG.error("Failed to raise hitch")
        return False

    async def __lower_hitch(self, attempts: int = 1) -> bool:
        LOG.info("Lowering hitch")
        for _ in range(attempts):
            try:
                await self._tractor.lower_hitch()
            except Exception:
                # HACK because board always times out for hitch change
                pass
            await asyncio.sleep(self._validator.cfg.hitch_change_time)
            if self._tractor.get_hitch_percent() < self._validator.cfg.hitch_lowered_max:
                return True
        LOG.error("Failed to lower hitch")
        return False

    async def __set_gear(self, gear: Gear, attempts: int = 1) -> bool:
        for _ in range(attempts):
            try:
                await self._tractor.set_gear(gear)
                await asyncio.sleep(1)
                if gear == self._tractor.get_gear():
                    return True
            except Exception:
                LOG.exception("Failed to set gear")
        return False

    async def __gear_enforcer(self, task_id: int, gear: Gear, stop_event: asyncio.Event) -> None:
        while not stop_event.is_set():
            curr = self._tractor.get_gear()
            if curr != gear:
                if not stop_event.is_set():
                    LOG.info(f"Gear enforcer detected gear mismatch. expected={gear}, actual={curr}")
                    stop_event.set()
                    await self.__intervention_req(task_id, True, "", "Gear enforcer detected gear mismatch")
                return
            try:
                await asyncio.wait_for(stop_event.wait(), 5)
            except asyncio.TimeoutError:
                pass

    async def __hitch_enforcer(
        self, task_id: int, error_case: Callable[[float], bool], stop_event: asyncio.Event
    ) -> None:
        while not stop_event.is_set():
            if error_case(self._tractor.get_hitch_percent()):
                if not stop_event.is_set():
                    msg = "Hitch position enforcer detected hitch mismatch."
                    LOG.info(msg)
                    stop_event.set()
                    await self.__intervention_req(task_id, True, "", msg)
                return
            try:
                await asyncio.wait_for(stop_event.wait(), 5)
            except asyncio.TimeoutError:
                pass

    async def __path_checker_enforcer(self, task_id: int, pc: PathChecker, stop_event: asyncio.Event) -> None:
        while not stop_event.is_set():
            state = await pc.check_on_path()
            if state != OnPathState.ON_PATH:
                stop_event.set()
                if state == OnPathState.PATH_COMPLETE:
                    LOG.info("Path has been completed stopping enforcer")
                else:
                    await self.__intervention_req(task_id, True, "", "Failed to follow path to completion")
                return
            try:
                await asyncio.wait_for(stop_event.wait(), 1)  # TODO should this be faster?
            except asyncio.TimeoutError:
                pass

    async def __atim_disable_watcher(self, task_id: int, stop_event: asyncio.Event) -> None:
        async def __change_cb(enabled: bool) -> None:
            if not enabled:
                await self.__intervention_req(
                    task_id, True, "", "ATIM has been disabled, cannot use implement controls"
                )

        try:
            listener = TRACTOR_EVENT_BUS.add_listener(ATIM_STATE_CHANGE, __change_cb)
            await stop_event.wait()
        finally:
            TRACTOR_EVENT_BUS.remove_listener(ATIM_STATE_CHANGE, listener)

    async def __pos_updater(self, task_id: int, stop_event: asyncio.Event) -> None:
        prev_pos_ts = int((time() - 1) * 1000)
        while not stop_event.is_set():
            cur_pos = await get_next_non_opt(prev_pos_ts)
            prev_pos_ts = cur_pos.timestamp_ms
            try:
                await self._job_client.update_latest_task_pos(task_id, cur_pos)
            except Exception as ex:
                LOG.error(f"Failed to update last position ex: {ex}")
            try:
                await asyncio.wait_for(stop_event.wait(), LAST_POS_UPDATE_DELTA_S)
            except asyncio.TimeoutError:
                pass

    async def __expected_state_handler(
        self, task_id: int, states: Iterable[jobs_pb.TractorState], stop_event: asyncio.Event
    ) -> Tuple[bool, List[Coroutine[Any, Any, None]]]:
        enforcers: List[Coroutine[Any, Any, None]] = []
        for state in states:
            if state.WhichOneof("state") == "gear":
                if state.gear in GearMap:
                    expected = GearMap[state.gear]
                    curr = self._tractor.get_gear()
                    if curr != expected:
                        LOG.info(f"Tractor not in expected state. Expected gear = {expected}, actual = {curr}")
                        if not await self.__set_gear(expected):
                            LOG.warning(f"Failed to set gear to {expected}")
                            return (False, enforcers)
                    enforcers.append(self.__gear_enforcer(task_id, expected, stop_event))
                else:
                    LOG.warning(f"Unknown expected gear state {state.gear}")
                    return (False, enforcers)
            elif state.WhichOneof("state") == "hitch":
                if state.hitch.WhichOneof("state") == "command":
                    if state.hitch.command == jobs_pb.HitchState.RAISED:
                        enforcers.append(
                            self.__hitch_enforcer(
                                task_id, lambda curr: curr < self._validator.cfg.hitch_raised_min, stop_event
                            )
                        )
                    elif state.hitch.command == jobs_pb.HitchState.LOWERED:
                        enforcers.append(
                            self.__hitch_enforcer(
                                task_id, lambda curr: curr > self._validator.cfg.hitch_lowered_max, stop_event
                            )
                        )
                    else:
                        LOG.warning("unsupported expected hitch state")
                else:
                    LOG.warning("unsupported expected hitch state")
            else:
                LOG.warning("Unknown state type")
        return (True, enforcers)

    async def _stopped(self) -> None:
        TRACTOR_EVENT_BUS.emit(AUTONOMOUS_TASK_RUNNER_STOPPED)

    async def _on_boundary_update(self, state: TractorBoundaryState) -> None:
        if self._disabled.is_set():
            return
        await self._do_boundary_check(state, ACTIVE_FARM.current)

    async def _do_boundary_check(self, state: TractorBoundaryState, farm: FarmAreas) -> None:
        task_id = 0
        if self._active_task is not None:
            task = self._active_task.get_active()
            if task is not None:
                task_id = task.id
        # TODO handle more cases later
        if state.touches(farm.private_road):
            await self.__intervention_req(
                task_id,
                True,
                "",
                "Task cannot continue, we have entered a private road",
                InterventionCause.SENSOR_TRIGGERED,
            )
